@import "tailwindcss";

@theme {
  --radius: 0.5rem;

  /* Light theme colors using modern OKLCH color space */
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.145 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.145 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.145 0 0);
  --color-primary: oklch(0.205 0 0);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.97 0 0);
  --color-secondary-foreground: oklch(0.205 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.556 0 0);
  --color-accent: oklch(0.97 0 0);
  --color-accent-foreground: oklch(0.205 0 0);
  --color-destructive: oklch(0.577 0.245 27.325);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.922 0 0);
  --color-input: oklch(0.922 0 0);
  --color-ring: oklch(0.708 0 0);

  /* Chart colors with P3 gamut */
  --color-chart-1: oklch(0.646 0.222 41.116);
  --color-chart-2: oklch(0.6 0.118 184.704);
  --color-chart-3: oklch(0.398 0.07 227.392);
  --color-chart-4: oklch(0.828 0.189 84.429);
  --color-chart-5: oklch(0.769 0.188 70.08);
}

.dark {
  /* Dark theme colors using modern OKLCH color space */
  --color-background: oklch(0.145 0 0);
  --color-foreground: oklch(0.985 0 0);
  --color-card: oklch(0.205 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.205 0 0);
  --color-popover-foreground: oklch(0.985 0 0);
  --color-primary: oklch(0.922 0 0);
  --color-primary-foreground: oklch(0.205 0 0);
  --color-secondary: oklch(0.269 0 0);
  --color-secondary-foreground: oklch(0.985 0 0);
  --color-muted: oklch(0.269 0 0);
  --color-muted-foreground: oklch(0.708 0 0);
  --color-accent: oklch(0.269 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-destructive: oklch(0.704 0.191 22.216);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.269 0 0);
  --color-input: oklch(0.269 0 0);
  --color-ring: oklch(0.556 0 0);

  /* Dark theme chart colors */
  --color-chart-1: oklch(0.488 0.243 264.376);
  --color-chart-2: oklch(0.696 0.17 162.48);
  --color-chart-3: oklch(0.769 0.188 70.08);
  --color-chart-4: oklch(0.627 0.265 303.9);
  --color-chart-5: oklch(0.645 0.246 16.439);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  /* Ensure dark mode transitions work properly */
  .dark {
    color-scheme: dark;
  }

  .light {
    color-scheme: light;
  }
}

/* React Flow custom styles */
/* Override React Flow default node widths to allow custom sizing */
.react-flow__node-input,
.react-flow__node-default,
.react-flow__node-output,
.react-flow__node-group {
  width: auto !important;
  min-width: auto !important;
  padding: 0 !important;
}

/* React Flow CSS variables for theme support */
:root {
  --xy-node-background-color: var(--color-card);
  --xy-node-border-color: var(--color-border);
  --xy-node-color: var(--color-card-foreground);
  --xy-node-border-radius: 0.5rem;
  --xy-node-boxshadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --xy-node-border-width: 1px;
  --xy-node-border: var(--xy-node-border-width) solid var(--xy-node-border-color);
}

.dark {
  --xy-node-background-color: var(--color-card);
  --xy-node-border-color: var(--color-border);
  --xy-node-color: var(--color-card-foreground);
}

/* Theme-responsive React Flow nodes */
.react-flow__node-input,
.react-flow__node-default,
.react-flow__node-output {
  background: var(--xy-node-background-color) !important;
  border: var(--xy-node-border) !important;
  border-radius: var(--xy-node-border-radius) !important;
  box-shadow: var(--xy-node-boxshadow) !important;
  color: var(--xy-node-color) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease !important;
}

/* Selected node styling */
.react-flow__node.selected {
  box-shadow: 0 0 0 2px var(--color-primary) !important;
}

/* React Flow minimap theme support */
.react-flow__minimap {
  background: var(--color-muted) !important;
  border: 1px solid var(--color-border) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

/* React Flow controls theme support */
.react-flow__controls {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

.react-flow__controls button {
  background: var(--color-background) !important;
  border-bottom: 1px solid var(--color-border) !important;
  color: var(--color-foreground) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease !important;
}

.react-flow__controls button:hover {
  background: var(--color-muted) !important;
}

/* React Flow animations */
@keyframes dash {
  to {
    stroke-dashoffset: -15;
  }
}
