DATABASE_URL="***********************************************************/postgres?pgbouncer=true&connect_timeout=30&pool_timeout=30"

DIRECT_URL="***********************************************************/postgres?connect_timeout=30"

# Next Auth
NEXTAUTH_SECRET="your-secret-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth (optional - get from https://discord.com/developers/applications)
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

# Google OAuth (optional - get from https://console.cloud.google.com)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Redis Configuration (Redis Cloud)
REDIS_HOST="your-redis-host.redns.redis-cloud.com"
REDIS_PORT="your-redis-port"
REDIS_USERNAME="your-redis-username"
REDIS_PASSWORD="your-redis-password"
REDIS_DB="your-database-name"

# Skip env validation during build (development only)
SKIP_ENV_VALIDATION=true
